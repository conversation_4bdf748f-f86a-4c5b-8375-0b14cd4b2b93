class Stopwatch {
    constructor() {
        this.time = 0; // Time in milliseconds
        this.interval = null;
        this.running = false;
        
        // Get DOM elements
        this.minutesDisplay = document.getElementById('minutes');
        this.secondsDisplay = document.getElementById('seconds');
        this.millisecondsDisplay = document.getElementById('milliseconds');
        this.startBtn = document.getElementById('startBtn');
        this.pauseBtn = document.getElementById('pauseBtn');
        this.resetBtn = document.getElementById('resetBtn');
        
        // Bind event listeners
        this.startBtn.addEventListener('click', () => this.start());
        this.pauseBtn.addEventListener('click', () => this.pause());
        this.resetBtn.addEventListener('click', () => this.reset());
        
        // Initialize display
        this.updateDisplay();
    }
    
    start() {
        if (!this.running) {
            this.running = true;
            this.startBtn.disabled = true;
            this.pauseBtn.disabled = false;
            
            this.interval = setInterval(() => {
                this.time += 10; // Increment by 10ms for smooth millisecond display
                this.updateDisplay();
            }, 10);
        }
    }
    
    pause() {
        if (this.running) {
            this.running = false;
            this.startBtn.disabled = false;
            this.pauseBtn.disabled = true;
            
            clearInterval(this.interval);
        }
    }
    
    reset() {
        this.running = false;
        this.time = 0;
        this.startBtn.disabled = false;
        this.pauseBtn.disabled = true;
        
        clearInterval(this.interval);
        this.updateDisplay();
    }
    
    updateDisplay() {
        // Calculate minutes, seconds, and milliseconds
        const totalSeconds = Math.floor(this.time / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        const milliseconds = Math.floor((this.time % 1000) / 10); // Display centiseconds (0-99)
        
        // Update display with zero-padding
        this.minutesDisplay.textContent = this.padZero(minutes);
        this.secondsDisplay.textContent = this.padZero(seconds);
        this.millisecondsDisplay.textContent = this.padZero(milliseconds);
    }
    
    padZero(num) {
        return num.toString().padStart(2, '0');
    }
}

// Initialize the stopwatch when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new Stopwatch();
});

// Add keyboard shortcuts
document.addEventListener('keydown', (event) => {
    const stopwatch = window.stopwatch;
    
    switch(event.code) {
        case 'Space':
            event.preventDefault();
            if (document.getElementById('startBtn').disabled) {
                document.getElementById('pauseBtn').click();
            } else {
                document.getElementById('startBtn').click();
            }
            break;
        case 'KeyR':
            event.preventDefault();
            document.getElementById('resetBtn').click();
            break;
    }
});

// Store stopwatch instance globally for keyboard shortcuts
document.addEventListener('DOMContentLoaded', () => {
    window.stopwatch = new Stopwatch();
});
