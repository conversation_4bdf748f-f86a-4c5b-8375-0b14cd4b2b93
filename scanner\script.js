class QRCodeApp {
    constructor() {
        this.scanner = null;
        this.currentCamera = 'environment'; // Start with back camera
        this.scanHistory = JSON.parse(localStorage.getItem('qr-scan-history')) || [];
        this.isScanning = false;
        
        this.initializeElements();
        this.initializeEventListeners();
        this.initializeTabs();
        this.loadScanHistory();
    }

    initializeElements() {
        // Scanner elements
        this.videoElement = document.getElementById('qr-video');
        this.startScanBtn = document.getElementById('start-scan');
        this.stopScanBtn = document.getElementById('stop-scan');
        this.switchCameraBtn = document.getElementById('switch-camera');
        this.scanResult = document.getElementById('scan-result');
        this.scanText = document.getElementById('scan-text');
        this.copyResultBtn = document.getElementById('copy-result');
        this.openLinkBtn = document.getElementById('open-link');
        this.historyList = document.getElementById('history-list');
        this.clearHistoryBtn = document.getElementById('clear-history');

        // Generator elements
        this.qrTextInput = document.getElementById('qr-text');
        this.qrSizeSelect = document.getElementById('qr-size');
        this.qrColorInput = document.getElementById('qr-color');
        this.qrBgColorInput = document.getElementById('qr-bg-color');
        this.generateBtn = document.getElementById('generate-qr');
        this.qrOutput = document.getElementById('qr-output');
        this.qrCanvas = document.getElementById('qr-canvas');
        this.downloadBtn = document.getElementById('download-qr');
        this.copyQrBtn = document.getElementById('copy-qr');

        // Message elements
        this.errorMessage = document.getElementById('error-message');
        this.successMessage = document.getElementById('success-message');

        // Tab elements
        this.tabButtons = document.querySelectorAll('.tab-btn');
        this.tabContents = document.querySelectorAll('.tab-content');
    }

    initializeEventListeners() {
        // Scanner event listeners
        this.startScanBtn.addEventListener('click', () => this.startScanning());
        this.stopScanBtn.addEventListener('click', () => this.stopScanning());
        this.switchCameraBtn.addEventListener('click', () => this.switchCamera());
        this.copyResultBtn.addEventListener('click', () => this.copyResult());
        this.openLinkBtn.addEventListener('click', () => this.openLink());
        this.clearHistoryBtn.addEventListener('click', () => this.clearHistory());

        // Generator event listeners
        this.generateBtn.addEventListener('click', () => this.generateQRCode());
        this.downloadBtn.addEventListener('click', () => this.downloadQRCode());
        this.copyQrBtn.addEventListener('click', () => this.copyQRCode());

        // Tab event listeners
        this.tabButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const tabName = e.target.closest('.tab-btn').dataset.tab;
                this.switchTab(tabName);
            });
        });
    }

    initializeTabs() {
        this.switchTab('scanner');
    }

    switchTab(tabName) {
        // Update tab buttons
        this.tabButtons.forEach(btn => {
            btn.classList.toggle('active', btn.dataset.tab === tabName);
        });

        // Update tab contents
        this.tabContents.forEach(content => {
            content.classList.toggle('active', content.id === tabName);
        });

        // Stop scanning when switching away from scanner tab
        if (tabName !== 'scanner' && this.isScanning) {
            this.stopScanning();
        }
    }

    async startScanning() {
        try {
            if (!QrScanner.hasCamera()) {
                throw new Error('No camera found on this device');
            }

            this.scanner = new QrScanner(
                this.videoElement,
                (result) => this.onScanSuccess(result),
                {
                    preferredCamera: this.currentCamera,
                    highlightScanRegion: true,
                    highlightCodeOutline: true,
                }
            );

            await this.scanner.start();
            this.isScanning = true;
            this.updateScannerUI(true);
            this.showSuccess('Camera started successfully');

        } catch (error) {
            console.error('Error starting scanner:', error);
            this.showError(`Failed to start camera: ${error.message}`);
        }
    }

    async stopScanning() {
        if (this.scanner) {
            this.scanner.stop();
            this.scanner.destroy();
            this.scanner = null;
        }
        this.isScanning = false;
        this.updateScannerUI(false);
        this.showSuccess('Camera stopped');
    }

    async switchCamera() {
        if (!this.scanner) return;

        try {
            this.currentCamera = this.currentCamera === 'environment' ? 'user' : 'environment';
            await this.scanner.setCamera(this.currentCamera);
            this.showSuccess(`Switched to ${this.currentCamera === 'environment' ? 'back' : 'front'} camera`);
        } catch (error) {
            console.error('Error switching camera:', error);
            this.showError('Failed to switch camera');
        }
    }

    onScanSuccess(result) {
        const scannedText = result.data;
        this.displayScanResult(scannedText);
        this.addToHistory(scannedText);
        this.showSuccess('QR Code scanned successfully!');
    }

    displayScanResult(text) {
        this.scanText.textContent = text;
        this.scanResult.style.display = 'block';

        // Check if the result is a URL
        const isUrl = this.isValidUrl(text);
        this.openLinkBtn.style.display = isUrl ? 'inline-flex' : 'none';
    }

    isValidUrl(string) {
        try {
            new URL(string);
            return true;
        } catch (_) {
            return false;
        }
    }

    copyResult() {
        const text = this.scanText.textContent;
        navigator.clipboard.writeText(text).then(() => {
            this.showSuccess('Result copied to clipboard');
        }).catch(() => {
            this.showError('Failed to copy result');
        });
    }

    openLink() {
        const text = this.scanText.textContent;
        if (this.isValidUrl(text)) {
            window.open(text, '_blank');
        }
    }

    addToHistory(text) {
        const timestamp = new Date().toLocaleString();
        const historyItem = { text, timestamp };
        
        // Add to beginning of array and limit to 10 items
        this.scanHistory.unshift(historyItem);
        this.scanHistory = this.scanHistory.slice(0, 10);
        
        // Save to localStorage
        localStorage.setItem('qr-scan-history', JSON.stringify(this.scanHistory));
        
        this.loadScanHistory();
    }

    loadScanHistory() {
        this.historyList.innerHTML = '';
        
        if (this.scanHistory.length === 0) {
            this.historyList.innerHTML = '<p style="text-align: center; color: #666;">No scan history yet</p>';
            return;
        }

        this.scanHistory.forEach(item => {
            const historyItem = document.createElement('div');
            historyItem.className = 'history-item';
            historyItem.innerHTML = `
                <div style="font-size: 0.8rem; color: #666; margin-bottom: 5px;">${item.timestamp}</div>
                <div>${item.text}</div>
            `;
            this.historyList.appendChild(historyItem);
        });
    }

    clearHistory() {
        this.scanHistory = [];
        localStorage.removeItem('qr-scan-history');
        this.loadScanHistory();
        this.showSuccess('History cleared');
    }

    updateScannerUI(isScanning) {
        this.startScanBtn.style.display = isScanning ? 'none' : 'inline-flex';
        this.stopScanBtn.style.display = isScanning ? 'inline-flex' : 'none';
        this.switchCameraBtn.style.display = isScanning ? 'inline-flex' : 'none';
    }

    async generateQRCode() {
        const text = this.qrTextInput.value.trim();
        
        if (!text) {
            this.showError('Please enter text to generate QR code');
            return;
        }

        try {
            const size = parseInt(this.qrSizeSelect.value);
            const color = this.qrColorInput.value;
            const bgColor = this.qrBgColorInput.value;

            await QRCode.toCanvas(this.qrCanvas, text, {
                width: size,
                height: size,
                color: {
                    dark: color,
                    light: bgColor
                },
                margin: 2,
                errorCorrectionLevel: 'M'
            });

            this.qrOutput.style.display = 'block';
            this.showSuccess('QR Code generated successfully!');

        } catch (error) {
            console.error('Error generating QR code:', error);
            this.showError('Failed to generate QR code');
        }
    }

    downloadQRCode() {
        const link = document.createElement('a');
        link.download = 'qrcode.png';
        link.href = this.qrCanvas.toDataURL();
        link.click();
        this.showSuccess('QR Code downloaded');
    }

    async copyQRCode() {
        try {
            this.qrCanvas.toBlob(blob => {
                const item = new ClipboardItem({ 'image/png': blob });
                navigator.clipboard.write([item]).then(() => {
                    this.showSuccess('QR Code copied to clipboard');
                }).catch(() => {
                    this.showError('Failed to copy QR Code');
                });
            });
        } catch (error) {
            this.showError('Failed to copy QR Code');
        }
    }

    showError(message) {
        this.errorMessage.textContent = message;
        this.errorMessage.style.display = 'block';
        setTimeout(() => {
            this.errorMessage.style.display = 'none';
        }, 5000);
    }

    showSuccess(message) {
        this.successMessage.textContent = message;
        this.successMessage.style.display = 'block';
        setTimeout(() => {
            this.successMessage.style.display = 'none';
        }, 3000);
    }
}

// Initialize the app when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new QRCodeApp();
});
