# QR Code Scanner & Generator

A modern, responsive web application that allows users to scan QR codes using their device's camera and generate new QR codes from text input.

## Features

### QR Code Scanner
- **Camera Access**: Uses device camera to scan QR codes in real-time
- **Multi-Camera Support**: Switch between front and back cameras
- **Real-time Scanning**: Instant QR code detection and decoding
- **Scan History**: Keeps track of the last 10 scanned codes with timestamps
- **Smart Link Detection**: Automatically detects URLs and provides "Open Link" option
- **Copy to Clipboard**: Easy copying of scanned results
- **Visual Feedback**: Animated scanner frame and success/error messages

### QR Code Generator
- **Text to QR**: Convert any text, URL, or data into QR codes
- **Customizable Size**: Choose from Small (200x200) to Extra Large (500x500)
- **Color Customization**: Set custom foreground and background colors
- **Download Support**: Save generated QR codes as PNG images
- **Copy to Clipboard**: Copy QR code images directly to clipboard
- **High Quality**: Generated codes use error correction for better scanning

### Additional Features
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Modern UI**: Clean, intuitive interface with smooth animations
- **Local Storage**: Scan history is saved locally in the browser
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **No Server Required**: Runs entirely in the browser

## How to Use

### Getting Started
1. Open `index.html` in a modern web browser
2. Allow camera permissions when prompted
3. Choose between Scanner or Generator tabs

### Scanning QR Codes
1. Click the "Scanner" tab
2. Click "Start Scanning" button
3. Point your camera at a QR code
4. The result will appear automatically below the camera view
5. Use "Copy" to copy the result or "Open Link" for URLs
6. View your scan history below the results

### Generating QR Codes
1. Click the "Generator" tab
2. Enter text, URL, or any data in the text area
3. Customize size and colors if desired
4. Click "Generate QR Code"
5. Download or copy the generated QR code

## Technical Requirements

### Browser Compatibility
- Chrome 63+ (recommended)
- Firefox 53+
- Safari 11+
- Edge 79+

### Device Requirements
- Camera access for scanning functionality
- HTTPS connection (required for camera access in most browsers)
- Modern JavaScript support (ES6+)

### Libraries Used
- **QR Scanner**: `qr-scanner@1.4.2` - For camera-based QR code scanning
- **QRCode.js**: `qrcode@1.5.3` - For QR code generation
- **Font Awesome**: `6.0.0` - For icons

## Installation & Setup

### Local Development
1. Clone or download the project files
2. Ensure all files are in the same directory:
   - `index.html`
   - `styles.css`
   - `script.js`
   - `README.md`

### Serving the Application
For camera access to work properly, the app must be served over HTTPS or localhost:

#### Option 1: Local Server (Python)
```bash
# Python 3
python -m http.server 8000

# Python 2
python -m SimpleHTTPServer 8000
```
Then visit: `http://localhost:8000`

#### Option 2: Local Server (Node.js)
```bash
npx http-server
```

#### Option 3: Live Server (VS Code Extension)
Install the "Live Server" extension in VS Code and right-click on `index.html` → "Open with Live Server"

## File Structure
```
scanner/
├── index.html          # Main HTML file
├── styles.css          # CSS styling
├── script.js           # JavaScript functionality
└── README.md           # This documentation
```

## Browser Permissions

### Camera Access
- The app requires camera permissions to scan QR codes
- Most browsers will prompt for permission on first use
- If denied, you can re-enable in browser settings

### Clipboard Access
- Copy functionality requires clipboard permissions
- Modern browsers handle this automatically for user-initiated actions

## Troubleshooting

### Camera Not Working
1. Ensure you're using HTTPS or localhost
2. Check browser permissions for camera access
3. Try refreshing the page and allowing permissions again
4. Test with a different browser

### QR Code Not Scanning
1. Ensure good lighting conditions
2. Hold the camera steady and at appropriate distance
3. Make sure the QR code is clearly visible and not damaged
4. Try switching cameras if available

### Generation Issues
1. Check that text input is not empty
2. Very long text may result in complex QR codes that are harder to scan
3. Ensure sufficient contrast between foreground and background colors

## Privacy & Security
- All processing happens locally in your browser
- No data is sent to external servers
- Scan history is stored only in your browser's local storage
- Camera feed is not recorded or transmitted

## License
This project is open source and available under the MIT License.
