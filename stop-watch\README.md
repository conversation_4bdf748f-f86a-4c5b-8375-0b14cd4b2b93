# Stopwatch App

A beautiful, responsive stopwatch application built with HTML, CSS, and JavaScript.

## Features

- **Precise Time Display**: Shows minutes, seconds, and centiseconds (hundredths of a second)
- **Start/Pause/Reset Controls**: Full control over the timer
- **Responsive Design**: Works on desktop and mobile devices
- **Keyboard Shortcuts**: 
  - `Spacebar`: Start/Pause the stopwatch
  - `R`: Reset the stopwatch
- **Modern UI**: Beautiful gradient background with glassmorphism effects

## How to Use

1. **Start the Stopwatch**: Click the "Start" button or press the spacebar
2. **Pause the Stopwatch**: Click the "Pause" button or press the spacebar while running
3. **Reset the Stopwatch**: Click the "Reset" button or press the "R" key

## Running the App

Simply open the `index.html` file in any modern web browser. No installation or build process required!

## File Structure

```
stop-watch/
├── index.html      # Main HTML file
├── styles.css      # Styling and responsive design
├── script.js       # Stopwatch functionality
└── README.md       # This file
```

## Technical Details

- **Time Precision**: Updates every 10 milliseconds for smooth display
- **Display Format**: MM:SS:CS (Minutes:Seconds:Centiseconds)
- **Browser Compatibility**: Works in all modern browsers
- **No Dependencies**: Pure HTML, CSS, and JavaScript

## Features Implemented

✅ Minutes, seconds, and milliseconds display  
✅ Start functionality  
✅ Pause functionality  
✅ Reset functionality  
✅ Responsive design  
✅ Keyboard shortcuts  
✅ Modern, attractive UI  

Enjoy using your new stopwatch app!
