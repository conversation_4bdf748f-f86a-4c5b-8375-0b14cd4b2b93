/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    color: white;
    font-size: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 10px;
}

header h1 i {
    margin-right: 15px;
}

/* Tab Navigation */
.tab-nav {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    background: rgba(255,255,255,0.1);
    border-radius: 50px;
    padding: 5px;
    backdrop-filter: blur(10px);
}

.tab-btn {
    flex: 1;
    max-width: 200px;
    padding: 15px 25px;
    border: none;
    background: transparent;
    color: white;
    font-size: 1.1rem;
    border-radius: 45px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.tab-btn:hover {
    background: rgba(255,255,255,0.2);
}

.tab-btn.active {
    background: white;
    color: #667eea;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Tab Content */
.tab-content {
    display: none;
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
}

.tab-content.active {
    display: block;
}

/* Scanner Section */
.scanner-section h2,
.generator-section h2 {
    text-align: center;
    margin-bottom: 25px;
    color: #333;
    font-size: 1.8rem;
}

.camera-container {
    position: relative;
    max-width: 400px;
    margin: 0 auto 25px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

#qr-video {
    width: 100%;
    height: auto;
    display: block;
}

.scanner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.scanner-frame {
    width: 200px;
    height: 200px;
    border: 3px solid #00ff00;
    border-radius: 15px;
    box-shadow: 0 0 20px rgba(0,255,0,0.5);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

/* Controls */
.controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102,126,234,0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102,126,234,0.6);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.9rem;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
}

/* Result Section */
.result-section {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 25px;
    border-left: 4px solid #667eea;
}

.result-section h3 {
    margin-bottom: 15px;
    color: #333;
}

.result-content {
    background: white;
    padding: 15px;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

#scan-text {
    word-break: break-all;
    margin-bottom: 15px;
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
}

.result-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

/* Generator Section */
.input-section {
    margin-bottom: 25px;
}

.input-section label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

#qr-text {
    width: 100%;
    min-height: 100px;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    resize: vertical;
    margin-bottom: 20px;
    transition: border-color 0.3s ease;
}

#qr-text:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102,126,234,0.1);
}

.options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.option-group {
    display: flex;
    flex-direction: column;
}

.option-group select,
.option-group input {
    padding: 10px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.option-group select:focus,
.option-group input:focus {
    outline: none;
    border-color: #667eea;
}

/* Output Section */
.output-section {
    text-align: center;
    background: #f8f9fa;
    border-radius: 15px;
    padding: 25px;
    border-left: 4px solid #28a745;
}

.qr-display {
    margin: 20px 0;
    display: flex;
    justify-content: center;
}

#qr-canvas {
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.output-actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

/* History Section */
.history-section {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
    border-left: 4px solid #ffc107;
}

.history-section h3 {
    margin-bottom: 15px;
    color: #333;
}

#history-list {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 15px;
}

.history-item {
    background: white;
    padding: 10px;
    margin-bottom: 8px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    word-break: break-all;
}

/* Messages */
.error-message,
.success-message {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

.error-message {
    background: #dc3545;
}

.success-message {
    background: #28a745;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .tab-content {
        padding: 20px;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
    }
    
    .options {
        grid-template-columns: 1fr;
    }
    
    .result-actions,
    .output-actions {
        flex-direction: column;
        align-items: center;
    }
}
