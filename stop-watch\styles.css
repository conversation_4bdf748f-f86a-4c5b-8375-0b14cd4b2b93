* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

.container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.stopwatch {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    text-align: center;
    min-width: 400px;
    backdrop-filter: blur(10px);
}

h1 {
    color: #333;
    margin-bottom: 30px;
    font-size: 2.5rem;
    font-weight: 300;
    letter-spacing: 2px;
}

.display {
    font-size: 4rem;
    font-weight: bold;
    color: #2c3e50;
    margin: 30px 0;
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 15px;
    border: 3px solid #e9ecef;
    letter-spacing: 3px;
}

.controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 30px;
}

.btn {
    padding: 15px 30px;
    font-size: 1.1rem;
    font-weight: bold;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    min-width: 100px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn:active {
    transform: translateY(0);
}

.start {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
}

.start:hover {
    background: linear-gradient(45deg, #218838, #1ba085);
}

.pause {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
    color: white;
}

.pause:hover {
    background: linear-gradient(45deg, #e0a800, #e8630a);
}

.reset {
    background: linear-gradient(45deg, #dc3545, #e83e8c);
    color: white;
}

.reset:hover {
    background: linear-gradient(45deg, #c82333, #d91a72);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

@media (max-width: 480px) {
    .stopwatch {
        min-width: 300px;
        padding: 30px 20px;
    }
    
    .display {
        font-size: 2.5rem;
        padding: 15px;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    .controls {
        flex-direction: column;
        gap: 10px;
    }
    
    .btn {
        padding: 12px 25px;
        font-size: 1rem;
    }
}
