<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>To-Do List App</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="app-header">
            <h1><i class="fas fa-tasks"></i> My To-Do List</h1>
            <p class="subtitle">Stay organized and productive</p>
        </header>

        <main class="app-main">
            <!-- Task Input Section -->
            <section class="task-input-section">
                <form id="taskForm" class="task-form">
                    <div class="input-group">
                        <input 
                            type="text" 
                            id="taskInput" 
                            placeholder="What needs to be done?" 
                            required
                            aria-label="Task description"
                            maxlength="200"
                        >
                        <select id="prioritySelect" aria-label="Task priority">
                            <option value="low">Low Priority</option>
                            <option value="medium" selected>Medium Priority</option>
                            <option value="high">High Priority</option>
                        </select>
                        <input 
                            type="date" 
                            id="dueDateInput" 
                            aria-label="Due date"
                        >
                        <button type="submit" class="add-btn">
                            <i class="fas fa-plus"></i> Add Task
                        </button>
                    </div>
                </form>
            </section>

            <!-- Controls Section -->
            <section class="controls-section">
                <div class="search-filter-group">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input 
                            type="text" 
                            id="searchInput" 
                            placeholder="Search tasks..."
                            aria-label="Search tasks"
                        >
                    </div>
                    
                    <div class="filter-buttons">
                        <button class="filter-btn active" data-filter="all">
                            All <span class="count" id="allCount">0</span>
                        </button>
                        <button class="filter-btn" data-filter="pending">
                            Pending <span class="count" id="pendingCount">0</span>
                        </button>
                        <button class="filter-btn" data-filter="completed">
                            Completed <span class="count" id="completedCount">0</span>
                        </button>
                    </div>
                </div>

                <div class="action-buttons">
                    <button id="exportBtn" class="action-btn">
                        <i class="fas fa-download"></i> Export
                    </button>
                    <button id="clearCompletedBtn" class="action-btn danger">
                        <i class="fas fa-trash"></i> Clear Completed
                    </button>
                </div>
            </section>

            <!-- Tasks Section -->
            <section class="tasks-section">
                <div id="tasksContainer" class="tasks-container">
                    <!-- Tasks will be dynamically inserted here -->
                </div>
                
                <div id="emptyState" class="empty-state">
                    <i class="fas fa-clipboard-list"></i>
                    <h3>No tasks yet</h3>
                    <p>Add your first task above to get started!</p>
                </div>
            </section>
        </main>

        <!-- Task Statistics -->
        <footer class="app-footer">
            <div class="stats">
                <span id="totalTasks">0 tasks total</span>
                <span id="completionRate">0% completed</span>
            </div>
        </footer>
    </div>

    <!-- Edit Task Modal -->
    <div id="editModal" class="modal" role="dialog" aria-labelledby="editModalTitle" aria-hidden="true">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="editModalTitle">Edit Task</h2>
                <button class="close-btn" aria-label="Close modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="editTaskForm" class="modal-form">
                <div class="form-group">
                    <label for="editTaskText">Task Description</label>
                    <input 
                        type="text" 
                        id="editTaskText" 
                        required
                        maxlength="200"
                    >
                </div>
                <div class="form-group">
                    <label for="editPriority">Priority</label>
                    <select id="editPriority">
                        <option value="low">Low Priority</option>
                        <option value="medium">Medium Priority</option>
                        <option value="high">High Priority</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="editDueDate">Due Date</label>
                    <input type="date" id="editDueDate">
                </div>
                <div class="modal-actions">
                    <button type="button" class="cancel-btn">Cancel</button>
                    <button type="submit" class="save-btn">Save Changes</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal" role="dialog" aria-labelledby="deleteModalTitle" aria-hidden="true">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="deleteModalTitle">Confirm Delete</h2>
                <button class="close-btn" aria-label="Close modal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this task? This action cannot be undone.</p>
                <div class="task-preview" id="deleteTaskPreview"></div>
            </div>
            <div class="modal-actions">
                <button type="button" class="cancel-btn">Cancel</button>
                <button type="button" class="delete-confirm-btn">Delete Task</button>
            </div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container" aria-live="polite"></div>

    <script src="script.js"></script>
</body>
</html>
