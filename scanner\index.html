<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Code Scanner & Generator</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-qrcode"></i> QR Code Scanner & Generator</h1>
        </header>

        <nav class="tab-nav">
            <button class="tab-btn active" data-tab="scanner">
                <i class="fas fa-camera"></i> Scanner
            </button>
            <button class="tab-btn" data-tab="generator">
                <i class="fas fa-plus-circle"></i> Generator
            </button>
        </nav>

        <!-- Scanner Tab -->
        <div id="scanner" class="tab-content active">
            <div class="scanner-section">
                <h2>Scan QR Code</h2>
                <div class="camera-container">
                    <video id="qr-video" autoplay muted playsinline></video>
                    <div class="scanner-overlay">
                        <div class="scanner-frame"></div>
                    </div>
                </div>
                
                <div class="controls">
                    <button id="start-scan" class="btn btn-primary">
                        <i class="fas fa-play"></i> Start Scanning
                    </button>
                    <button id="stop-scan" class="btn btn-secondary" style="display: none;">
                        <i class="fas fa-stop"></i> Stop Scanning
                    </button>
                    <button id="switch-camera" class="btn btn-secondary" style="display: none;">
                        <i class="fas fa-sync-alt"></i> Switch Camera
                    </button>
                </div>

                <div id="scan-result" class="result-section" style="display: none;">
                    <h3>Scan Result:</h3>
                    <div class="result-content">
                        <p id="scan-text"></p>
                        <div class="result-actions">
                            <button id="copy-result" class="btn btn-small">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                            <button id="open-link" class="btn btn-small" style="display: none;">
                                <i class="fas fa-external-link-alt"></i> Open Link
                            </button>
                        </div>
                    </div>
                </div>

                <div id="scan-history" class="history-section">
                    <h3>Scan History</h3>
                    <div id="history-list"></div>
                    <button id="clear-history" class="btn btn-small btn-danger">
                        <i class="fas fa-trash"></i> Clear History
                    </button>
                </div>
            </div>
        </div>

        <!-- Generator Tab -->
        <div id="generator" class="tab-content">
            <div class="generator-section">
                <h2>Generate QR Code</h2>
                
                <div class="input-section">
                    <label for="qr-text">Enter text or URL:</label>
                    <textarea id="qr-text" placeholder="Enter text, URL, or any data to generate QR code..."></textarea>
                    
                    <div class="options">
                        <div class="option-group">
                            <label for="qr-size">Size:</label>
                            <select id="qr-size">
                                <option value="200">Small (200x200)</option>
                                <option value="300" selected>Medium (300x300)</option>
                                <option value="400">Large (400x400)</option>
                                <option value="500">Extra Large (500x500)</option>
                            </select>
                        </div>
                        
                        <div class="option-group">
                            <label for="qr-color">Color:</label>
                            <input type="color" id="qr-color" value="#000000">
                        </div>
                        
                        <div class="option-group">
                            <label for="qr-bg-color">Background:</label>
                            <input type="color" id="qr-bg-color" value="#ffffff">
                        </div>
                    </div>
                    
                    <button id="generate-qr" class="btn btn-primary">
                        <i class="fas fa-magic"></i> Generate QR Code
                    </button>
                </div>

                <div id="qr-output" class="output-section" style="display: none;">
                    <h3>Generated QR Code:</h3>
                    <div class="qr-display">
                        <canvas id="qr-canvas"></canvas>
                    </div>
                    <div class="output-actions">
                        <button id="download-qr" class="btn btn-primary">
                            <i class="fas fa-download"></i> Download
                        </button>
                        <button id="copy-qr" class="btn btn-secondary">
                            <i class="fas fa-copy"></i> Copy Image
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div id="error-message" class="error-message" style="display: none;"></div>
        <div id="success-message" class="success-message" style="display: none;"></div>
    </div>

    <!-- Include QR Scanner and Generator Libraries -->
    <script src="https://unpkg.com/qr-scanner@1.4.2/qr-scanner.umd.min.js"></script>
    <script src="https://unpkg.com/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
