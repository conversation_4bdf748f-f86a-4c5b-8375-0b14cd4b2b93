/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #667eea;
    --primary-dark: #5a6fd8;
    --secondary-color: #764ba2;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    --border-color: #e5e7eb;
    --border-light: #f3f4f6;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --transition: all 0.2s ease-in-out;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 1rem;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Header Styles */
.app-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 2rem;
    text-align: center;
}

.app-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-header h1 i {
    margin-right: 0.5rem;
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Main Content */
.app-main {
    padding: 2rem;
}

/* Task Input Section */
.task-input-section {
    margin-bottom: 2rem;
}

.input-group {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    align-items: center;
}

#taskInput {
    flex: 1;
    min-width: 250px;
    padding: 0.75rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    transition: var(--transition);
}

#taskInput:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

#prioritySelect, #dueDateInput {
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    background: var(--bg-primary);
    transition: var(--transition);
}

#prioritySelect:focus, #dueDateInput:focus {
    outline: none;
    border-color: var(--primary-color);
}

.add-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.add-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Controls Section */
.controls-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-light);
}

.search-filter-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.search-box {
    position: relative;
    flex: 1;
    min-width: 200px;
    max-width: 300px;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-light);
}

#searchInput {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    transition: var(--transition);
}

#searchInput:focus {
    outline: none;
    border-color: var(--primary-color);
}

.filter-buttons {
    display: flex;
    gap: 0.5rem;
}

.filter-btn {
    padding: 0.5rem 1rem;
    border: 2px solid var(--border-color);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-btn:hover {
    border-color: var(--primary-color);
    background: var(--bg-secondary);
}

.filter-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.count {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.2rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.filter-btn.active .count {
    background: rgba(255, 255, 255, 0.3);
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.action-btn {
    padding: 0.5rem 1rem;
    border: 2px solid var(--border-color);
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    font-size: 0.9rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-btn:hover {
    background: var(--bg-secondary);
}

.action-btn.danger {
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.action-btn.danger:hover {
    background: var(--danger-color);
    color: white;
}

/* Tasks Section */
.tasks-container {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.task-item {
    background: var(--bg-primary);
    border: 2px solid var(--border-light);
    border-radius: var(--radius-md);
    padding: 1rem;
    transition: var(--transition);
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.task-item:hover {
    border-color: var(--border-color);
    box-shadow: var(--shadow-sm);
}

.task-item.completed {
    opacity: 0.7;
    background: var(--bg-secondary);
}

.task-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.task-checkbox {
    width: 1.25rem;
    height: 1.25rem;
    cursor: pointer;
    accent-color: var(--success-color);
}

.task-details {
    flex: 1;
    min-width: 0;
}

.task-text {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
    word-wrap: break-word;
    transition: var(--transition);
}

.task-item.completed .task-text {
    text-decoration: line-through;
    color: var(--text-light);
}

.task-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 0.8rem;
    color: var(--text-secondary);
    flex-wrap: wrap;
}

.priority-badge {
    padding: 0.2rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
}

.priority-high {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.priority-medium {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.priority-low {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.due-date {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.due-date.overdue {
    color: var(--danger-color);
    font-weight: 600;
}

.task-actions {
    display: flex;
    gap: 0.5rem;
}

.task-btn {
    padding: 0.5rem;
    border: none;
    background: transparent;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-secondary);
}

.task-btn:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.task-btn.edit:hover {
    color: var(--primary-color);
}

.task-btn.delete:hover {
    color: var(--danger-color);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: var(--text-light);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

/* Footer */
.app-footer {
    background: var(--bg-secondary);
    padding: 1rem 2rem;
    border-top: 1px solid var(--border-light);
}

.stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    animation: fadeIn 0.2s ease-out;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    animation: scaleIn 0.2s ease-out;
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-light);
}

.modal-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-light);
    transition: var(--transition);
}

.close-btn:hover {
    color: var(--text-primary);
}

.modal-body {
    padding: 1.5rem;
}

.modal-form {
    padding: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    transition: var(--transition);
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding: 1.5rem;
    border-top: 1px solid var(--border-light);
}

.cancel-btn, .save-btn, .delete-confirm-btn {
    padding: 0.75rem 1.5rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
}

.cancel-btn {
    background: var(--bg-primary);
    color: var(--text-primary);
}

.cancel-btn:hover {
    background: var(--bg-secondary);
}

.save-btn {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.save-btn:hover {
    background: var(--primary-dark);
}

.delete-confirm-btn {
    background: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

.delete-confirm-btn:hover {
    background: #dc2626;
}

.task-preview {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: var(--radius-md);
    margin-top: 1rem;
    font-style: italic;
    color: var(--text-secondary);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1100;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.toast {
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 1rem;
    box-shadow: var(--shadow-lg);
    min-width: 300px;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.toast.success {
    border-color: var(--success-color);
    background: rgba(16, 185, 129, 0.05);
}

.toast.error {
    border-color: var(--danger-color);
    background: rgba(239, 68, 68, 0.05);
}

.toast.info {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 0.5rem;
    }
    
    .container {
        border-radius: 0;
        min-height: 100vh;
    }
    
    .app-header {
        padding: 1.5rem 1rem;
    }
    
    .app-header h1 {
        font-size: 2rem;
    }
    
    .app-main {
        padding: 1rem;
    }
    
    .input-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    #taskInput {
        min-width: auto;
    }
    
    .search-filter-group {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .action-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .task-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .task-details {
        width: 100%;
    }
    
    .task-meta {
        justify-content: space-between;
    }
    
    .stats {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }
    
    .toast {
        min-width: auto;
        width: calc(100vw - 2rem);
    }
    
    .toast-container {
        left: 1rem;
        right: 1rem;
    }
}

@media (max-width: 480px) {
    .app-header h1 {
        font-size: 1.75rem;
    }
    
    .filter-btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
    
    .action-btn {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }
    
    .task-actions {
        flex-direction: column;
        gap: 0.25rem;
    }
}

/* Print Styles */
@media print {
    body {
        background: white;
        padding: 0;
    }
    
    .container {
        box-shadow: none;
        border-radius: 0;
    }
    
    .app-header {
        background: white !important;
        color: black !important;
    }
    
    .controls-section,
    .task-actions,
    .app-footer {
        display: none;
    }
    
    .task-item {
        border: 1px solid #ccc;
        break-inside: avoid;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus Styles for Better Accessibility */
button:focus-visible,
input:focus-visible,
select:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }
}
